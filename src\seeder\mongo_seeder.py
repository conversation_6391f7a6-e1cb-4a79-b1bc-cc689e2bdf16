import json
import os
import sys
from pymongo import MongoClient
from bson import ObjectId
from datetime import datetime

# MongoDB Atlas connection
MONGO_URI = "mongodb+srv://Jo:<EMAIL>/py?retryWrites=true&w=majority&appName=Cluster0"
client = MongoClient(MONGO_URI)

db = client['py']
collection = db['py1']  # single document per app_id

files_to_seed = [
    ("new_collection_data.json", "validation_rules"),
    ("hennessy.prompts.json", "prompts"),
    ("hennessy.Workitem.json", "workitems"),
    ("hennessy.Wordblock.json", "wordblocks")
]

def convert_extended_json(obj):
    if isinstance(obj, dict):
        if "$oid" in obj:
            return ObjectId(obj["$oid"])
        if "$date" in obj:
            return datetime.fromisoformat(obj["$date"].replace("Z", "+00:00"))
        return {k: convert_extended_json(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_extended_json(item) for item in obj]
    else:
        return obj

def seed(app_id: str):
    container = {
        "app_id": app_id,
        "created_at": datetime.utcnow(),
        "source_data": {}
    }

    for file_name, tag in files_to_seed:
        if not os.path.exists(file_name):
            print(f"❌ Missing: {file_name}")
            continue

        with open(file_name, 'r', encoding='utf-8') as f:
            raw = json.load(f)
            parsed = convert_extended_json(raw)
            container["source_data"][tag] = parsed

    collection.delete_many({ "app_id": app_id })  # optional overwrite
    collection.insert_one(container)
    print(f"✅ Inserted single grouped record for app_id: {app_id}")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("❌ Usage: python seeder.py <app_id>")
        sys.exit(1)

    app_id_arg = sys.argv[1]
    seed(app_id_arg)
