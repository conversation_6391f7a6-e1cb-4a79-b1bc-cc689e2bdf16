import json
import openai
import os
import re
from datetime import datetime
from common.aria_helper.boto3_utils import get_secret, trigger_lambda
from common.aria_helper.mongo_utils import Mongo

# === Load Secrets and API Keys ===
common_prefix = f"{os.environ['ENV']}-{os.environ['PROJECT_NAME']}"
llm_secret = json.loads(get_secret(f'{common_prefix}-llm_params'))
openai.api_key = llm_secret['openai_api_key']
mongo_uri = get_secret(f'{common_prefix}-mongodb_uri', return_json=False).strip('"')


# === Mongo Setup ===
mongo = Mongo(mongo_uri)
db_name = os.environ.get("MONGO_DATABASE")


# === Utility: Extract word-level text from blocks ===
def get_document_text(blocks):
    return " ".join([b.get("Text", "") for b in blocks if b.get("BlockType") == "WORD"])


# === Utility: Build Prompt ===
def build_prompt(doc_type, document_text):
    mongo.select_db_and_collection(db_name, "prompts")
    prompt_doc = mongo.find_one({"document_type": doc_type})
    if not prompt_doc:
        raise ValueError(f"No prompt found for document type '{doc_type}'")
    return prompt_doc["content"].replace("{{ document_text }}", document_text)


# === Utility: Call OpenAI ===
def call_gpt(prompt):
    response = openai.ChatCompletion.create(
        model="gpt-4",
        messages=[
            {"role": "system", "content": "Extract structured fields from the word-level OCR blocks."},
            {"role": "user", "content": prompt}
        ],
        temperature=0
    )
    return response.choices[0].message.content.strip()


# === Utility: Normalize Field Name ===
def to_snake_case(name):
    return re.sub(r'[\s/-]+', '_', name.strip().lower())


# === Utility: Find Coordinates for a Value ===
def find_coordinates(value, blocks):
    def normalize(text):
        return re.sub(r'\s+', ' ', text.strip().lower())

    norm_value = normalize(value)
    words = [
        (b["Text"], b["Geometry"]["BoundingBox"], b.get("Page", 1), b.get("Confidence", 100))
        for b in blocks if b.get("BlockType") == "WORD"
    ]
    tokens = norm_value.split()
    for i in range(len(words) - len(tokens) + 1):
        window = words[i:i + len(tokens)]
        if normalize(" ".join(w[0] for w in window)) == norm_value:
            x0 = min(b["Left"] for _, b, _, _ in window)
            y0 = min(b["Top"] for _, b, _, _ in window)
            x1 = max(b["Left"] + b["Width"] for _, b, _, _ in window)
            y1 = max(b["Top"] + b["Height"] for _, b, _, _ in window)
            conf = [c for _, _, _, c in window]
            return {
                "normalized": {
                    "left": x0,
                    "top": y0,
                    "width": x1 - x0,
                    "height": y1 - y0
                }
            }, window[0][2], sum(conf) / len(conf)
    return {}, None, 0.0


# === Process One Document Group ===
def process_document(doc_type, workitem_id):
    mongo.select_db_and_collection(db_name, "Wordblock")
    wordblock_doc = mongo.find_one({"source_file": f"{doc_type}.json"})
    if not wordblock_doc:
        return

    blocks = wordblock_doc.get("Blocks", [])
    document_text = get_document_text(blocks)
    prompt = build_prompt(doc_type, document_text)
    gpt_output = call_gpt(prompt)

    try:
        raw_fields = json.loads(gpt_output)
    except json.JSONDecodeError:
        return

    fields = {}
    for key, value in raw_fields.items():
        field_name = to_snake_case(key)
        coords, page, confidence = find_coordinates(value, blocks)
        fields[field_name] = {
            "value": value,
            "confidence": round(confidence, 5),
            "coordinates": coords,
            "pageno": page
        }

    mongo.select_db_and_collection(db_name, "Workitem")
    mongo.update_one(
        {"id": workitem_id},
        {
            "$set": {
                f"groups.{doc_type}.fields": fields,
                f"groups.{doc_type}.ocr_data.ocr_status": 1,
                f"groups.{doc_type}.ocr_data.extracted_at": datetime.utcnow()
            }
        }
    )


# === Lambda Entry Point ===
def lambda_handler(event, context):
    try:
        body = event if isinstance(event, dict) else json.loads(event["body"])
        workitem_id = body.get("workitem_id")
        groups = body.get("groups", {})

        if not workitem_id:
            raise ValueError("Missing workitem_id")
        if not isinstance(groups, dict) or not groups:
            raise ValueError("Missing or invalid groups")

        for doc_type in groups:
            ocr_data = groups[doc_type].get("ocr_data", {})
            if ocr_data.get("ocr_status") == 1:
                continue
            process_document(doc_type, workitem_id)

        # Trigger next lambda: Validation
        trigger_lambda(os.environ.get('BRE_VALIDATION_FUNCTION', 'bre_validation'), {"workitem_id": workitem_id})

        return {
            "statusCode": 200,
            "body": json.dumps(f"LLM processed and forwarded workitem {workitem_id}")
        }

    except Exception as e:
        return {
            "statusCode": 500,
            "body": json.dumps(f"LLM Lambda Error: {str(e)}")
        }
